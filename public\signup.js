// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
    authDomain: "barefoot-elearning-app.firebaseapp.com",
    projectId: "barefoot-elearning-app",
    databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
    storageBucket: "barefoot-elearning-app.appspot.com",
    messagingSenderId: "170819735788",
    appId: "1:170819735788:web:223af318437eb5d947d5c9"
};

firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();

const personalDomains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
    'icloud.com', 'protonmail.com', 'mail.com', 'zoho.com', 'yandex.com',
    'live.com', 'msn.com', 'me.com', 'mac.com', 'googlemail.com'
];

// Function to detect lead source from referrer
function detectLeadSource() {
    const referrer = document.referrer;
    const urlParams = new URLSearchParams(window.location.search);
    
    // Check URL parameters first (highest priority)
    if (urlParams.get('source')) return urlParams.get('source');
    if (urlParams.get('utm_source')) return urlParams.get('utm_source');
    
    // Check session storage
    const sessionSource = sessionStorage.getItem('userSource');
    if (sessionSource) return sessionSource;
    
    // Detect from referrer
    if (referrer) {
        const referrerDomain = new URL(referrer).hostname.toLowerCase();
        
        if (referrerDomain.includes('facebook.com') || referrerDomain.includes('fb.com')) {
            return 'facebook';
        } else if (referrerDomain.includes('instagram.com')) {
            return 'instagram';
        } else if (referrerDomain.includes('google.com')) {
            // Check if it's from Google Ads
            if (referrer.includes('gclid=') || urlParams.get('gclid')) {
                return 'google_ads';
            }
            return 'google_search';
        } else if (referrerDomain.includes('linkedin.com')) {
            return 'linkedin';
        } else if (referrerDomain.includes('twitter.com') || referrerDomain.includes('t.co')) {
            return 'twitter';
        } else if (referrerDomain.includes('youtube.com')) {
            return 'youtube';
        } else if (referrerDomain.includes('bing.com')) {
            return 'bing_search';
        } else {
            return 'referral_' + referrerDomain;
        }
    }
    
    return 'direct';
}



function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

const loadingAnimation = lottie.loadAnimation({
    container: document.getElementById('loading-animation'),
    renderer: 'svg',
    loop: true,
    autoplay: false,
    path: 'assess_loading.json', // Changed from loading.json
});

const successAnimation = lottie.loadAnimation({
    container: document.querySelector('.success-icon'), // Update selector to match the SVG container
    renderer: 'svg',
    loop: false,
    autoplay: false,
    path: 'success.json'
});

const form = document.getElementById('signupForm');
const loadingOverlay = document.getElementById('loading-overlay');
const successOverlay = document.getElementById('success-overlay');


function showLoadingOverlay() {
    console.log('Displaying loading overlay');
    if (window.LoadingOverlay) {
        window.LoadingOverlay.show('signup', 'Creating account...');
    } else {
        loadingOverlay.style.display = 'flex';
        loadingAnimation.play();
    }
}

function hideLoadingOverlay() {
    console.log('Hiding loading overlay');
    if (window.LoadingOverlay) {
        window.LoadingOverlay.hide();
    } else {
        loadingOverlay.style.display = 'none';
        loadingAnimation.stop();
    }
}

successAnimation.addEventListener('complete', () => {
    setTimeout(() => {
        window.location.href = 'index.html?registration=success';
    }, 1500);
});

function showError(elementId, message) {
    console.log(`Error on ${elementId}: ${message}`);
    const errorElement = document.getElementById(elementId + 'Error');
    const inputElement = document.getElementById(elementId);

    errorElement.textContent = message;
    errorElement.style.display = 'block';
    inputElement.classList.add('error');

    // Add shake animation
    inputElement.classList.add('shake');
    setTimeout(() => {
        inputElement.classList.remove('shake');
    }, 500);
}

// Function to hide error
function hideError(elementId) {
    const errorElement = document.getElementById(elementId + 'Error');
    const inputElement = document.getElementById(elementId);

    errorElement.style.display = 'none';
    inputElement.classList.remove('error');
}

// Function to validate form
function validateForm() {
    console.log('Validating form inputs');
    let isValid = true;
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const firstname = document.getElementById('firstname').value.trim();
    const lastname = document.getElementById('lastname').value.trim();
    const company = document.getElementById('company').value.trim();
    const terms = document.getElementById('termsCheckbox').checked;

    ['email', 'password', 'confirmPassword', 'firstname', 'lastname', 'company'].forEach(hideError);

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
        showError('email', 'Email is required');
        isValid = false;
    } else if (!emailRegex.test(email)) {
        showError('email', 'Please enter a valid email address');
        isValid = false;
    }

    if (!password) {
        showError('password', 'Password is required');
        isValid = false;
    } else if (password.length < 6) {
        showError('password', 'Password must be at least 6 characters');
        isValid = false;
    }


    if (password !== confirmPassword) {
        showError('confirmPassword', 'Passwords do not match');
        isValid = false;
    }

    // Name validation
    if (!firstname) {
        showError('firstname', 'First name is required');
        isValid = false;
    }
    if (!lastname) {
        showError('lastname', 'Last name is required');
        isValid = false;
    }

    if (!company) {
        showError('company', 'Company name is required');
        isValid = false;
    }

    if (!terms) {
        alert('Please accept the terms and conditions');
        isValid = false;
    }

    console.log('Form validation completed:', isValid ? 'Valid' : 'Invalid');
    return isValid;
}

form.addEventListener('submit', async (e) => {
    e.preventDefault();
    console.log('Form submission initiated');

    if (!validateForm()) {
        console.log('Form validation failed');
        return;
    }

    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const firstname = document.getElementById('firstname').value.trim();
    const lastname = document.getElementById('lastname').value.trim();
    const company = document.getElementById('company').value.trim();
    const phoneNumber = document.getElementById('phoneNumber').value.trim();
    const referralCode = document.getElementById('referralCode')?.value?.trim()?.toUpperCase();

    // Additional security check to prevent personal email bypass
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
        hideLoadingOverlay();
        showError('email', emailValidation.message);
        showNotification('Please use your company email address to create an account.', 'error');
        return;
    }

    showLoadingOverlay();

    try {
        console.log('Creating user with Firebase Auth');
        // First check if email is already registered
        const methods = await firebase.auth().fetchSignInMethodsForEmail(email);
        if (methods && methods.length > 0) {
            hideLoadingOverlay();
            showError('email', 'This email address is already registered. Please try logging in instead.');
            showNotification('This account already exists. You can use the login page to access your dashboard.', 'error');
            return;
        }

        const userCredential = await firebase.auth().createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;

        console.log('Account created successfully - email verification will be handled later');
        // Email verification is now postponed until user tries to send invitations
        // This improves the signup flow by reducing interruptions

        console.log('Adding admin to Firestore');
        try {
            // Check if user came from April_expo landing page
            const userSource = sessionStorage.getItem('userSource');

            // Capture lead source from URL parameters or session storage
            const leadSource = detectLeadSource();
            const urlParams = new URLSearchParams(window.location.search);
            
            // Capture additional campaign data
            const campaignData = {
                source: leadSource,
                medium: urlParams.get('utm_medium') || 'unknown',
                campaign: urlParams.get('utm_campaign') || 'unknown',
                content: urlParams.get('utm_content') || null,
                term: urlParams.get('utm_term') || null,
                referrer: document.referrer || 'direct'
            };
            
            // Calculate free trial end date (14 days from now)
            const freeTrialEndDate = new Date();
            freeTrialEndDate.setDate(freeTrialEndDate.getDate() + 14);

            // Process referral code if provided
            let referrerData = null;
            let bonusCredits = 0;
            if (referralCode) {
                try {
                    console.log('Processing referral code:', referralCode);
                    const referrerQuery = await db.collection('Admins')
                        .where('referralCode', '==', referralCode)
                        .limit(1)
                        .get();

                    if (!referrerQuery.empty) {
                        referrerData = referrerQuery.docs[0];
                        bonusCredits = 5; // Bonus credits for using referral code
                        console.log('Valid referral code found, adding bonus credits');
                    } else {
                        console.log('Invalid referral code provided');
                        showNotification('Invalid referral code provided, but signup will continue normally.', 'warning');
                    }
                } catch (referralError) {
                    console.error('Error processing referral code:', referralError);
                    showNotification('Error processing referral code, but signup will continue normally.', 'warning');
                }
            }

            // Generate referral code for new user
            function generateReferralCode() {
                const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let result = '';
                for (let i = 0; i < 8; i++) {
                    result += characters.charAt(Math.floor(Math.random() * characters.length));
                }
                return result;
            }

            const newUserReferralCode = generateReferralCode();

            const adminData = {
                firstname,
                lastname,
                company,
                email,
                phoneNumber: phoneNumber || null, // Include phone number if provided
                credits: 5 + bonusCredits, // Base 5 credits + referral bonus if applicable
                status: 'Active',
                createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                hasCompletedTour: false,
                paid: false,
                subscriptionType: 'freeTrial', // Automatically assign free trial
                subscriptionActive: true,
                subscriptionStartDate: firebase.firestore.FieldValue.serverTimestamp(),
                subscriptionEndDate: firebase.firestore.Timestamp.fromDate(freeTrialEndDate),
                hasUsedFreeTrial: true, // Mark that they've used their free trial
                isNewUser: true, // Flag to identify new users for welcome modal
                // Referral system
                referralCode: newUserReferralCode,
                referralStats: {
                    totalReferrals: 0,
                    successfulReferrals: 0,
                    creditsEarned: 0
                },
                // Lead source tracking
                leadSource: leadSource,
                campaignData: campaignData,
                signupTimestamp: firebase.firestore.FieldValue.serverTimestamp()
            };

            // Add referral information if user was referred
            if (referrerData) {
                adminData.wasReferred = true;
                adminData.referredBy = referrerData.id;
                adminData.referralCodeUsed = referralCode;
            }

            // If user came from April_expo, add source information
            if (userSource === 'April_expo') {
                adminData.source = 'April_expo';
            }

            await db.collection('Admins').doc(user.email).set(adminData);

            // Update referrer's statistics and credits if this was a referral
            if (referrerData) {
                try {
                    console.log('Updating referrer statistics');
                    const referrerEmail = referrerData.id;
                    const referrerDocData = referrerData.data();

                    // Update referrer's stats and credits
                    const currentStats = referrerDocData.referralStats || { totalReferrals: 0, successfulReferrals: 0, creditsEarned: 0 };
                    const currentCredits = referrerDocData.credits || 0;

                    await db.collection('Admins').doc(referrerEmail).update({
                        credits: currentCredits + 5, // Give referrer 5 bonus credits
                        'referralStats.totalReferrals': (currentStats.totalReferrals || 0) + 1,
                        'referralStats.successfulReferrals': (currentStats.successfulReferrals || 0) + 1,
                        'referralStats.creditsEarned': (currentStats.creditsEarned || 0) + 5
                    });

                    console.log('Referrer statistics updated successfully');
                    showNotification(`Referral successful! You and your referrer both received 5 bonus credits.`, 'success');
                } catch (referrerUpdateError) {
                    console.error('Error updating referrer statistics:', referrerUpdateError);
                    // Don't block signup if referrer update fails
                }
            }
              // Create a separate lead tracking document
            try {
                await db.collection('leadSources').add({
                    email: email,
                    firstname: firstname,
                    lastname: lastname,
                    company: company,
                    leadSource: leadSource,
                    campaignData: campaignData,
                    signupDate: firebase.firestore.FieldValue.serverTimestamp(),
                    userAgent: navigator.userAgent,
                    ipAddress: null, // Will be populated by server if needed
                    conversionType: 'signup'
                });
                console.log('Lead source tracking data saved successfully');
                
                // Send additional tracking data to server for IP capture
                try {
                    await fetch('/track-lead-source', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: email,
                            leadSource: leadSource,
                            campaignData: campaignData,
                            userAgent: navigator.userAgent
                        })
                    });
                    console.log('Server-side lead tracking completed');
                } catch (serverTrackingError) {
                    console.error('Error with server-side tracking:', serverTrackingError);
                    // Don't block signup if server tracking fails
                }
            } catch (leadTrackingError) {
                console.error('Error saving lead tracking data:', leadTrackingError);
                // Don't block signup if lead tracking fails
            }
        } catch (dbError) {
            console.error('Error adding admin to database:', dbError);
            hideLoadingOverlay();
            showNotification('Your account was created but we encountered an issue setting up your profile. Please contact support.', 'warning');
            return;
        }

        // Critical path: Company document creation (required for dashboard functionality)
        try {
            const companyDocRef = db.collection('companies').doc(company);
            const companyDoc = await companyDocRef.get();
            if (!companyDoc.exists) {
                console.log('Adding company to Firestore');
                await companyDocRef.set({
                    name: company,
                    adminEmail: email,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                });
            } else {
                console.log('Company already exists in Firestore');
            }
        } catch (companyError) {
            console.error('Error handling company data:', companyError);
            hideLoadingOverlay();
            showNotification('Your account was created but we encountered an issue with company setup. Please contact support.', 'warning');
            return;
        }

        // ✅ CRITICAL PATH COMPLETE - Show success immediately
        console.log('Critical signup operations completed successfully');
        hideLoadingOverlay();
        console.log('Displaying success animation');
        successOverlay.classList.remove('hidden');
        successAnimation.play();

        // Set up success animation completion handler
        const onSuccessAnimationComplete = () => {
            console.log('Success animation completed');
            successAnimation.removeEventListener('complete', onSuccessAnimationComplete);
            setTimeout(() => {
                window.location.href = 'index.html?registration=success';
            }, 1500);
        };
        successAnimation.addEventListener('complete', onSuccessAnimationComplete);

        // 🔄 BACKGROUND OPERATIONS - Run asynchronously without blocking user experience
        console.log('Starting background operations...');
        runBackgroundOperations({
            firstname,
            lastname,
            email,
            company,
            phoneNumber,
            leadSource,
            campaignData,
            userSource: sessionStorage.getItem('userSource')
        });


    } catch (error) {
        hideLoadingOverlay();
        console.error('Error during signup:', error);

        switch (error.code) {
            case 'auth/email-already-in-use':
                showError('email', 'This email is already registered. Please try logging in.');
                showNotification('An account with this email already exists. Please use the login page.', 'error');
                break;
            case 'auth/invalid-email':
                showError('email', 'Please enter a valid email address.');
                break;
            case 'auth/weak-password':
                showError('password', 'Password is too weak. Please use at least 6 characters with a mix of letters, numbers, and symbols.');
                break;
            case 'auth/operation-not-allowed':
                showError('email', 'Account creation is currently disabled. Please contact support.');
                showNotification('Account creation is temporarily disabled. Please try again later or contact support.', 'error');
                break;
            case 'auth/network-request-failed':
                showNotification('Network error. Please check your internet connection and try again.', 'error');
                break;
            case 'auth/too-many-requests':
                showNotification('Too many requests. Please try again later.', 'error');
                break;
            default:
                showNotification(`An error occurred: ${error.message}. Please try again or contact support.`, 'error');
        }
    }
});

// Background operations function - runs asynchronously after user sees success
async function runBackgroundOperations(userData) {
    const {
        firstname,
        lastname,
        email,
        company,
        phoneNumber,
        leadSource,
        campaignData,
        userSource
    } = userData;

    console.log('🔄 Background operations started for:', email);

    // Define all background operations
    const backgroundTasks = [
        // Lead source tracking
        async () => {
            try {
                console.log('📊 Background: Saving lead source tracking data');
                await db.collection('leadSources').add({
                    email: email,
                    firstname: firstname,
                    lastname: lastname,
                    company: company,
                    leadSource: leadSource,
                    campaignData: campaignData,
                    signupDate: firebase.firestore.FieldValue.serverTimestamp(),
                    userAgent: navigator.userAgent,
                    ipAddress: null,
                    conversionType: 'signup'
                });
                console.log('✅ Background: Lead source tracking saved successfully');
            } catch (error) {
                console.error('❌ Background: Error saving lead tracking data:', error);
            }
        },

        // Server-side lead tracking
        async () => {
            try {
                console.log('🌐 Background: Sending server-side lead tracking');
                const response = await fetch('/track-lead-source', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: email,
                        leadSource: leadSource,
                        campaignData: campaignData,
                        userAgent: navigator.userAgent
                    })
                });
                if (response.ok) {
                    console.log('✅ Background: Server-side lead tracking completed');
                } else {
                    console.error('❌ Background: Server-side lead tracking failed:', response.status);
                }
            } catch (error) {
                console.error('❌ Background: Error with server-side tracking:', error);
            }
        },

        // April_expo record update
        async () => {
            if (userSource === 'April_expo') {
                try {
                    console.log('🎪 Background: Updating April_expo record');
                    const expoSnapshot = await db.collection('April_expo')
                        .where('email', '==', email)
                        .limit(1)
                        .get();

                    if (!expoSnapshot.empty) {
                        const expoDoc = expoSnapshot.docs[0];
                        await expoDoc.ref.update({
                            signupCompleted: true,
                            signupTimestamp: firebase.firestore.FieldValue.serverTimestamp()
                        });
                        console.log('✅ Background: April_expo record updated successfully');
                    }
                } catch (error) {
                    console.error('❌ Background: Error updating April_expo record:', error);
                }
            }
        },

        // Zoho CRM integration
        async () => {
            try {
                console.log('🏢 Background: Sending user data to Zoho CRM');
                const response = await fetch('/add-to-zoho-crm', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        firstname,
                        lastname,
                        email,
                        company
                    })
                });

                const result = await response.json();
                if (result.success) {
                    console.log('✅ Background: User data sent to Zoho CRM successfully');
                } else {
                    console.error('❌ Background: Failed to send user data to Zoho CRM:', result.message);
                }
            } catch (error) {
                console.error('❌ Background: Error sending user data to Zoho CRM:', error);
            }
        },

        // Registration notification email (multiple recipients)
        async () => {
            try {
                console.log('📧 Background: Sending registration notification emails to multiple recipients');
                const response = await fetch('/send-registration-notification', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        firstname,
                        lastname,
                        email,
                        company,
                        phoneNumber: phoneNumber || null,
                        registrationTimestamp: new Date().toISOString()
                    })
                });

                const result = await response.json();
                if (result.results) {
                    const { sent, failed, total } = result.results;
                    console.log(`✅ Background: Registration notifications - ${sent.length}/${total} sent successfully`);

                    if (sent.length > 0) {
                        console.log(`📧 Sent to: ${sent.join(', ')}`);
                    }

                    if (failed.length > 0) {
                        console.log(`❌ Failed recipients: ${failed.map(f => f.recipient).join(', ')}`);
                    }

                    // Store detailed results for enhanced logging
                    this.notificationDetails = result.results;
                } else {
                    console.error('❌ Background: Failed to send registration notifications:', result.error);
                }
            } catch (error) {
                console.error('❌ Background: Error sending registration notifications:', error);
            }
        }
    ];

    // Execute all background tasks in parallel using Promise.allSettled
    console.log(`🚀 Background: Starting ${backgroundTasks.length} parallel operations`);
    const results = await Promise.allSettled(backgroundTasks.map(task => task()));

    // Log summary of background operations
    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;

    console.log(`🎯 Background operations completed: ${successful} successful, ${failed} failed`);

    // Prepare detailed logging data
    const operationNames = [
        'Lead Source Tracking',
        'Server-side Lead Tracking',
        'April Expo Update',
        'Zoho CRM Integration',
        'Registration Notification'
    ];

    const operationResults = results.map((result, index) => {
        const baseResult = {
            name: operationNames[index],
            status: result.status,
            message: result.status === 'fulfilled' ? 'Completed successfully' : result.reason?.message || 'Unknown error',
            error: result.status === 'rejected' ? result.reason?.message : null
        };

        // Add enhanced details for registration notifications
        if (operationNames[index] === 'Registration Notification' && result.status === 'fulfilled') {
            // Try to access notification details from the task context
            // Note: This is a simplified approach - in a real implementation,
            // you might want to return details from the async function
            baseResult.details = {
                sent: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
                failed: [],
                total: 5
            };
        }

        return baseResult;
    });

    // Send logging data to server for monitoring
    try {
        await fetch('/log-background-operations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: email,
                operations: operationResults,
                timestamp: new Date().toISOString(),
                summary: { successful, failed, total: backgroundTasks.length }
            })
        });
    } catch (loggingError) {
        console.error('❌ Failed to send background operations log to server:', loggingError);
    }

    if (failed > 0) {
        console.log('❌ Failed background operations:',
            results
                .filter(result => result.status === 'rejected')
                .map(result => result.reason)
        );
    }
}

document.querySelectorAll('input').forEach((input) => {
    input.addEventListener('input', () => {
        hideError(input.id);
    });
});

// Password visibility toggle
function setupPasswordToggles() {
    const passwordGroups = document.querySelectorAll('.password-group');

    passwordGroups.forEach((group) => {
        const input = group.querySelector('input');
        const toggleButton = group.querySelector('.password-toggle');
        const eyeOpen = toggleButton.querySelector('.eye-open');
        const eyeClosed = toggleButton.querySelector('.eye-closed');

        toggleButton.addEventListener('click', (e) => {
            e.preventDefault();
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);

            // Toggle visibility of eye icons
            eyeOpen.classList.toggle('hidden', type === 'text');
            eyeClosed.classList.toggle('hidden', type === 'password');
        });
    });
}

setupPasswordToggles();



window.addEventListener('load', () => {
    const params = new URLSearchParams(window.location.search);

    // Check for registration success message
    if (params.get('registration') === 'success') {
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message';
        successMessage.textContent = 'Welcome to Skills Assess! Your free trial has been activated with 5 assessment credits. You can now log in to your account.';
        document.querySelector('.login-heading').appendChild(successMessage);
    }

    // Auto-fill form fields from URL parameters (from landing pages)
    const firstname = params.get('firstname');
    const lastname = params.get('lastname');
    const email = params.get('email');
    const company = params.get('company');
    const source = params.get('source');
    const referralCode = params.get('ref');

    // If we have data from the landing page, fill in the form
    if (firstname && lastname && email && company) {
        document.getElementById('firstname').value = firstname;
        document.getElementById('lastname').value = lastname;
        document.getElementById('email').value = email;
        document.getElementById('company').value = company;

        // If the source is April_expo, store this information
        if (source === 'April_expo') {
            // We'll use sessionStorage to remember this for later
            sessionStorage.setItem('userSource', 'April_expo');
        }

        // Trigger email validation
        if (email) {
            const emailInput = document.querySelector('input[type="email"]');
            if (emailInput) {
                const event = new Event('input', { bubbles: true });
                emailInput.dispatchEvent(event);
            }
        }
    }

    // If we have a referral code from URL, fill it in
    if (referralCode) {
        const referralInput = document.getElementById('referralCode');
        if (referralInput) {
            referralInput.value = referralCode.toUpperCase();
        }
    }
});

// Cache DOM elements
const emailInput = document.querySelector('input[type="email"]');
const emailValidIcon = document.getElementById('emailValidIcon');
const emailInvalidIcon = document.getElementById('emailInvalidIcon');
const signupButton = document.querySelector('.signup-button');

// Email validation function
function validateEmail(email) {
    if (!email) return { isValid: false, message: '' }; // No message when empty
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(email)) {
        return { isValid: false, message: 'Please enter a valid email address' };
    }

    const domain = email.split('@')[1]?.toLowerCase();
    if (personalDomains.includes(domain)) {
        return { isValid: false, message: 'Please use your company email address' };
    }

    return { isValid: true, message: '' };
}

// Phone number validation function
function validatePhoneNumber(phone) {
    if (!phone) return { isValid: true, message: '' }; // Optional field, empty is valid

    // Remove all non-digit characters for validation
    const cleanPhone = phone.replace(/\D/g, '');

    // Check if it's a reasonable length (7-15 digits is typical for international numbers)
    if (cleanPhone.length < 7 || cleanPhone.length > 15) {
        return { isValid: false, message: 'Please enter a valid phone number (7-15 digits)' };
    }

    // Basic format check - allow common formats like +1234567890, (*************, ************, etc.
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{7,20}$/;
    if (!phoneRegex.test(phone)) {
        return { isValid: false, message: 'Please enter a valid phone number format' };
    }

    return { isValid: true, message: '' };
}



function updateEmailUI(validationResult, email) {
    const emailValidIcon = document.getElementById('emailValidIcon');
    const emailInvalidIcon = document.getElementById('emailInvalidIcon');
    const emailInput = document.querySelector('input[type="email"]');
    const signupButton = document.querySelector('.signup-button');

    // Update icons
    emailValidIcon.classList.toggle('hidden', !validationResult.isValid);
    emailInvalidIcon.classList.toggle('hidden', validationResult.isValid);

    // Remove existing validation styles
    emailInput.classList.remove('border-[#ef4444]', 'border-[#10B981]', 'bg-white');

    if (validationResult.isValid) {
        emailInput.classList.add('border-[#10B981]', 'bg-white');
        hideError('email');
        const domain = email.split('@')[1];
        showNotification(`Valid company email address: ${domain}`, 'success');
    } else if (email && validationResult.message) {
        emailInput.classList.add('border-[#ef4444]', 'bg-white');
        hideError('email'); // Hide any existing error first
        showError('email', validationResult.message);
        if (personalDomains.includes(email.split('@')[1]?.toLowerCase())) {
            showNotification('Looks like you entered a personal email address. Please use your work email instead', 'warning');
        }
    }

    // Update signup button state
    if (signupButton) {
        signupButton.disabled = !validationResult.isValid;
        signupButton.classList.toggle('opacity-50', !validationResult.isValid);
        signupButton.classList.toggle('cursor-not-allowed', !validationResult.isValid);
    }
}


function showError(elementId, message) {
    const errorElement = document.getElementById(elementId + 'Error');
    const inputElement = document.getElementById(elementId) || document.querySelector(`input[name="${elementId}"]`);

    if (errorElement && inputElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        inputElement.classList.add('error');

        inputElement.classList.add('shake');
        setTimeout(() => {
            inputElement.classList.remove('shake');
        }, 500);
    }
}

function hideError(elementId) {
    const errorElement = document.getElementById(elementId + 'Error');
    const inputElement = document.getElementById(elementId) || document.querySelector(`input[name="${elementId}"]`);

    if (errorElement && inputElement) {
        errorElement.style.display = 'none';
        inputElement.classList.remove('error');
    }
}

const handleEmailInput = debounce((e) => {
    const email = e.target.value.trim();
    const validationResult = validateEmail(email);
    updateEmailUI(validationResult, email);
}, 500);

if (emailInput) {
    emailInput.addEventListener('input', handleEmailInput);

    emailInput.addEventListener('focus', () => {
        emailInput.classList.add('bg-white', 'border-[#6366f1]', 'shadow-[0_0_0_3px_rgba(99,102,241,0.1)]');
    });

    emailInput.addEventListener('blur', () => {
        if (!emailInput.value.trim()) {
            emailInput.classList.remove('bg-white', 'border-[#6366f1]', 'shadow-[0_0_0_3px_rgba(99,102,241,0.1)]');
        }
    });
}

const originalValidateForm = validateForm;

function validateForm() {
    let isValid = true;
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const firstname = document.getElementById('firstname').value.trim();
    const lastname = document.getElementById('lastname').value.trim();
    const company = document.getElementById('company').value.trim();
    const phoneNumber = document.getElementById('phoneNumber').value.trim();
    const terms = document.getElementById('termsCheckbox').checked;

    // Clear all previous errors
    ['email', 'password', 'confirmPassword', 'firstname', 'lastname', 'company', 'phoneNumber'].forEach(hideError);

    // Email validation
    if (!email) {
        showError('email', 'Email is required');
        isValid = false;
    } else {
        const emailValidation = validateEmail(email);
        if (!emailValidation.isValid) {
            showError('email', emailValidation.message);
            isValid = false;
        }
    }

    // Phone number validation (optional field)
    if (phoneNumber) {
        const phoneValidation = validatePhoneNumber(phoneNumber);
        if (!phoneValidation.isValid) {
            showError('phoneNumber', phoneValidation.message);
            isValid = false;
        }
    }

    // Password validation
    if (!password) {
        showError('password', 'Password is required');
        isValid = false;
    } else if (password.length < 6) {
        showError('password', 'Password must be at least 6 characters');
        isValid = false;
    }

    // Confirm password validation
    if (password !== confirmPassword) {
        showError('confirmPassword', 'Passwords do not match');
        isValid = false;
    }

    // Name validation
    if (!firstname) {
        showError('firstname', 'First name is required');
        isValid = false;
    }
    if (!lastname) {
        showError('lastname', 'Last name is required');
        isValid = false;
    }

    // Company validation
    if (!company) {
        showError('company', 'Company name is required');
        isValid = false;
    }

    // Terms validation
    if (!terms) {
        alert('Please accept the terms and conditions');
        isValid = false;
    }

    return isValid;
}

function showNotification(message, type = 'success') {
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    const notificationContainer = document.createElement('div');
    notificationContainer.classList.add('notification');

    Object.assign(notificationContainer.style, {
        position: 'fixed',
        top: '70px',
        left: '50%',
        transform: 'translateX(-50%)',
        padding: '12px 24px',
        borderRadius: '6px',
        opacity: '0',
        transition: 'opacity 0.3s ease',
        zIndex: '9999',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        fontWeight: '500'
    });
    if (type === 'success') {
        notificationContainer.classList.add('success');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#48bb78',
            color: 'white'
        });
    } else if (type === 'error') {
        notificationContainer.classList.add('error');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#FEE2E2',
            color: '#991B1B',
            border: '1px solid #FCA5A5'
        });
    } else if (type === 'warning') {
        notificationContainer.classList.add('warning');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#FEF3C7',
            color: '#92400E',
            border: '1px solid #FCD34D'
        });
    }

    notificationContainer.textContent = message;

    document.body.appendChild(notificationContainer);

    requestAnimationFrame(() => {
        notificationContainer.style.opacity = '1';
    });


    setTimeout(() => {
        notificationContainer.style.opacity = '0';
        setTimeout(() => {
            notificationContainer.remove();
        }, 300);
    }, 4000);
}

function setupEmailValidation() {
    const emailInput = document.querySelector('input[type="email"]');
    if (!emailInput) return;

    emailInput.addEventListener('input', handleEmailInput);

    emailInput.addEventListener('focus', () => {
        emailInput.classList.add('bg-white', 'border-[#6366f1]', 'shadow-[0_0_0_3px_rgba(99,102,241,0.1)]');
    });

    emailInput.addEventListener('blur', () => {
        if (!emailInput.value.trim()) {
            emailInput.classList.remove('bg-white', 'border-[#6366f1]', 'shadow-[0_0_0_3px_rgba(99,102,241,0.1)]');
        }
    });
}

document.addEventListener('DOMContentLoaded', setupEmailValidation);